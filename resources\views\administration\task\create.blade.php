@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Create New Task'))

@section('css_links')
    {{--  External CSS  --}}
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/typography.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/katex.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/editor.css') }}" />

    {{-- Bootstrap Datepicker --}}
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css') }}" />

    {{-- Dropzone CSS --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css" />
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
    /* Custom Dropzone Styling */
    .dropzone {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        background: #f8fafc;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .dropzone:hover {
        border-color: #6366f1;
        background: #f1f5f9;
    }

    .dropzone.dz-drag-hover {
        border-color: #6366f1;
        background: #e0e7ff;
    }

    .dropzone .dz-message {
        margin: 0;
        font-size: 16px;
        color: #6b7280;
    }

    .dropzone .dz-message .dz-button {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        font: inherit;
        outline: inherit;
        padding: 0;
    }

    .dropzone .dz-preview {
        margin: 10px;
        min-height: auto;
    }

    .dropzone .dz-preview .dz-image {
        border-radius: 6px;
        width: 80px;
        height: 80px;
    }

    .dropzone .dz-preview .dz-details {
        padding: 10px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 6px;
    }

    .dropzone .dz-preview .dz-remove {
        color: #ef4444;
        text-decoration: none;
        font-size: 12px;
        border: 1px solid #ef4444;
        padding: 2px 6px;
        border-radius: 4px;
        background: white;
        margin-top: 5px;
        display: inline-block;
    }

    .dropzone .dz-preview .dz-remove:hover {
        background: #ef4444;
        color: white;
    }

    .dropzone .dz-preview .dz-progress {
        height: 4px;
        border-radius: 2px;
        background: #e5e7eb;
        overflow: hidden;
    }

    .dropzone .dz-preview .dz-upload {
        background: #6366f1;
        height: 100%;
        border-radius: 2px;
    }

    .dropzone .dz-preview .dz-error-message {
        color: #ef4444;
        font-size: 12px;
        margin-top: 5px;
    }

    .dropzone .dz-preview .dz-success-mark,
    .dropzone .dz-preview .dz-error-mark {
        display: none;
    }
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Create New Task') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">{{ __('Tasks') }}</li>
    <li class="breadcrumb-item active">{{ __('Create New Task') }}</li>
@endsection

@section('content')

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Create New Task</h5>

                <div class="card-header-elements ms-auto">
                    <a href="{{ route('administration.task.index') }}" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-circle ti-xs me-1"></span>
                        All Tasks
                    </a>
                </div>
            </div>
            <!-- Account -->
            <div class="card-body">
                <form id="taskForm" action="{{ route('administration.task.store') }}" method="post" enctype="multipart/form-data" autocomplete="off">
                    @csrf
                    <div class="row">
                        <div class="mb-3 col-md-8">
                            <label for="parent_task_id" class="form-label">{{ __('Select Parent Task') }}</label>
                            <select name="parent_task_id" id="parent_task_id" class="select2 form-select @error('parent_task_id') is-invalid @enderror" data-allow-clear="true">
                                <option value="" {{ is_null(request()->parent_task_id) ? 'selected' : '' }}>Select Parent Task</option>
                                @foreach ($tasks as $task)
                                    <option value="{{ $task->id }}" {{ $task->id == request()->parent_task_id ? 'selected' : '' }}>
                                        {{ $task->title }} - ({{ $task->taskid }})
                                    </option>
                                @endforeach
                            </select>
                            <small class="text-muted">
                                <span class="text-dark text-bold">Note:</span> If you select a parent task, the task will be created as a sub-task of the selected parent task.
                            </small>
                            @error('parent_task_id')
                                <b class="text-danger"><i class="feather icon-info mr-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-4">
                            <label class="form-label">Deadline</label>
                            <input type="text" name="deadline" value="{{ old('deadline') }}" class="form-control  date-picker" placeholder="YYYY-MM-DD" tabindex="-1"/>
                            <small class="text-muted">
                                <span class="text-dark text-bold">Note:</span> Leave it blank if you want to create an ongoing task.
                            </small>
                            @error('deadline')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="title" class="form-label">{{ __('Title') }} <strong class="text-danger">*</strong></label>
                            <input type="text" id="title" name="title" value="{{ old('title') }}" placeholder="{{ __('Title') }}" class="form-control @error('title') is-invalid @enderror" required/>
                            @error('title')
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="users" class="form-label">Select Users <strong class="text-danger">*</strong></label>
                            <select name="users[]" id="users" class="select2 form-select @error('users') is-invalid @enderror" data-allow-clear="true" multiple required>
                                <option value="selectAllValues">Select All</option>
                                @foreach ($roles as $role)
                                    <optgroup label="{{ $role->name }}">
                                        @foreach ($role->users as $user)
                                            <option value="{{ $user->id }}" {{ in_array($user->id, old('users', [])) ? 'selected' : '' }}>
                                                {{ get_employee_name($user) }}
                                            </option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            </select>
                            @error('users')
                                <b class="text-danger"><i class="feather icon-info mr-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="priority" class="form-label">Select Priority <strong class="text-danger">*</strong></label>
                            <div class="row">
                                <div class="col-md mb-md-0 mb-2">
                                    <div class="form-check custom-option custom-option-basic">
                                        <label class="form-check-label custom-option-content" for="priorityLow">
                                            <input name="priority" class="form-check-input" type="radio" value="Low" id="priorityLow" checked />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0">Low</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md">
                                    <div class="form-check custom-option custom-option-basic">
                                        <label class="form-check-label custom-option-content" for="priorityAverage">
                                            <input name="priority" class="form-check-input" type="radio" value="Average" id="priorityAverage" />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0">Average</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md">
                                    <div class="form-check custom-option custom-option-basic">
                                        <label class="form-check-label custom-option-content" for="priorityMedium">
                                            <input name="priority" class="form-check-input" type="radio" value="Medium" id="priorityMedium" />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0">Medium</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md">
                                    <div class="form-check custom-option custom-option-basic">
                                        <label class="form-check-label custom-option-content" for="priorityHigh">
                                            <input name="priority" class="form-check-input" type="radio" value="High" id="priorityHigh" />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0">High</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label">Task Description <strong class="text-danger">*</strong></label>
                            <div name="description" id="full-editor">{!! old('description') !!}</div>
                            <textarea class="d-none" name="description" id="description-input">{{ old('description') }}</textarea>
                            @error('description')
                                <b class="text-danger">{{ $message }}</b>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="task-files-dropzone" class="form-label">{{ __('Task Files') }}</label>
                            <div id="task-files-dropzone" class="dropzone">
                                <div class="dz-message">
                                    <i class="ti ti-cloud-upload" style="font-size: 48px; color: #6b7280; margin-bottom: 10px;"></i>
                                    <div>
                                        <strong>Drop files here or click to upload</strong>
                                        <br>
                                        <small class="text-muted">
                                            Maximum 5 files, 5MB each<br>
                                            Supported: JPG, PNG, PDF, DOC, DOCX, XLS, XLSX, ZIP
                                        </small>
                                    </div>
                                </div>
                            </div>
                            @error('files')
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i>{{ $message }}</b>
                            @enderror
                            @error('files.*')
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                    </div>
                    <div class="mt-2 float-end">
                        <a href="{{ route('administration.task.create') }}" class="btn btn-outline-danger me-2 confirm-danger">Reset Form</a>
                        <button type="submit" class="btn btn-primary">Create Task</button>
                    </div>
                </form>
            </div>
            <!-- /Account -->
        </div>
    </div>
</div>
<!-- End row -->

@endsection

@section('script_links')
    {{--  External Javascript Links --}}
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/js/form-layouts.js') }}"></script>
    <!-- Vendors JS -->
    <script src="{{ asset('assets/vendor/libs/quill/katex.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/quill.js') }}"></script>

    <script src="{{ asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js') }}"></script>

    {{-- Dropzone JS --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
@endsection

@section('custom_script')
    {{--  External Custom Javascript  --}}
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.date-picker').datepicker({
                format: 'yyyy-mm-dd',
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote", "code-block"],
                [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
            ];

            var fullEditor = new Quill("#full-editor", {
                bounds: "#full-editor",
                placeholder: "Ex: Mr. John Doe got promoted as Manager",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old description if validation fails
            @if(old('description'))
                fullEditor.root.innerHTML = {!! json_encode(old('description')) !!};
            @endif

            $('#taskForm').on('submit', function() {
                $('#description-input').val(fullEditor.root.innerHTML);
            });
        });
    </script>

    <script>
        // Dropzone Configuration
        Dropzone.autoDiscover = false;

        $(document).ready(function() {
            // Initialize Dropzone
            var taskFilesDropzone = new Dropzone("#task-files-dropzone", {
                url: "#", // Dummy URL since we're not using auto upload
                autoProcessQueue: false,
                uploadMultiple: true,
                parallelUploads: 5,
                maxFiles: 5,
                maxFilesize: 5, // MB
                acceptedFiles: ".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.zip",
                addRemoveLinks: true,
                dictDefaultMessage: "",
                dictRemoveFile: "Remove",
                dictFileTooBig: "File is too big ({{filesize}}MB). Max filesize: {{maxFilesize}}MB.",
                dictInvalidFileType: "You can't upload files of this type.",
                dictMaxFilesExceeded: "You can only upload {{maxFiles}} files.",
                previewTemplate: `
                    <div class="dz-preview dz-file-preview">
                        <div class="dz-image">
                            <img data-dz-thumbnail />
                        </div>
                        <div class="dz-details">
                            <div class="dz-size"><span data-dz-size></span></div>
                            <div class="dz-filename"><span data-dz-name></span></div>
                        </div>
                        <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
                        <div class="dz-error-message"><span data-dz-errormessage></span></div>
                        <div class="dz-success-mark">✓</div>
                        <div class="dz-error-mark">✗</div>
                        <a class="dz-remove" href="javascript:undefined;" data-dz-remove>Remove</a>
                    </div>
                `,
                init: function() {
                    var myDropzone = this;

                    // Handle form submission
                    $('#taskForm').on('submit', function(e) {
                        e.preventDefault();

                        // Show loading state
                        var submitBtn = $(this).find('button[type="submit"]');
                        var originalText = submitBtn.text();
                        submitBtn.prop('disabled', true).text('Creating Task...');

                        // Create FormData from the form
                        var formData = new FormData(this);

                        // Add files from dropzone
                        var files = myDropzone.getAcceptedFiles();
                        for (var i = 0; i < files.length; i++) {
                            formData.append('files[]', files[i]);
                        }

                        // Submit with AJAX
                        $.ajax({
                            url: $(this).attr('action'),
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response, textStatus, xhr) {
                                // The backend redirects on success, so we'll get HTML back
                                // We can detect this and redirect accordingly
                                if (xhr.getResponseHeader('content-type').indexOf('text/html') !== -1) {
                                    // Success - redirect to task list
                                    window.location.href = "{{ route('administration.task.index') }}";
                                }
                            },
                            error: function(xhr) {
                                // Reset button state
                                submitBtn.prop('disabled', false).text(originalText);

                                if (xhr.status === 422) {
                                    // Validation errors
                                    var errors = xhr.responseJSON.errors;

                                    // Clear previous errors
                                    $('.is-invalid').removeClass('is-invalid');
                                    $('.text-danger').remove();

                                    // Show new errors
                                    $.each(errors, function(field, messages) {
                                        var input = $('[name="' + field + '"]');
                                        if (input.length === 0 && field.includes('.')) {
                                            // Handle array fields like files.0, files.1, etc.
                                            input = $('#task-files-dropzone');
                                        }

                                        if (input.length > 0) {
                                            input.addClass('is-invalid');
                                            input.after('<b class="text-danger"><i class="ti ti-info-circle mr-1"></i>' + messages[0] + '</b>');
                                        }
                                    });

                                    // Scroll to first error
                                    var firstError = $('.is-invalid').first();
                                    if (firstError.length) {
                                        $('html, body').animate({
                                            scrollTop: firstError.offset().top - 100
                                        }, 500);
                                    }
                                } else if (xhr.status === 302) {
                                    // Redirect response - follow it
                                    var redirectUrl = xhr.getResponseHeader('Location');
                                    if (redirectUrl) {
                                        window.location.href = redirectUrl;
                                    } else {
                                        window.location.href = "{{ route('administration.task.index') }}";
                                    }
                                } else {
                                    // Other errors
                                    alert('An error occurred while creating the task. Please try again.');
                                }
                            }
                        });
                    });
                },
                error: function(file, message) {
                    console.log('Dropzone error:', message);
                }
            });
        });
    </script>
@endsection
