{"__meta": {"id": "01K0BZN9S61WSH2BXYFQW1A3G7", "datetime": "2025-07-17 16:42:39", "utime": **********.528122, "method": "POST", "uri": "/task/store", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752748951.90553, "end": **********.528146, "duration": 7.6226160526275635, "duration_str": "7.62s", "measures": [{"label": "Booting", "start": 1752748951.90553, "relative_start": 0, "end": **********.015156, "relative_end": **********.015156, "duration": 1.***************, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.015186, "relative_start": 1.****************, "end": **********.528149, "relative_end": 2.86102294921875e-06, "duration": 6.***************, "duration_str": "6.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.077662, "relative_start": 1.****************, "end": **********.094651, "relative_end": **********.094651, "duration": 0.*****************, "duration_str": "16.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.52298, "relative_start": 7.***************, "end": **********.523735, "relative_end": **********.523735, "duration": 0.0007550716400146484, "duration_str": "755μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST task/store", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Task Create", "controller": "App\\Http\\Controllers\\Administration\\Task\\TaskController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=91\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.task.store", "prefix": "/task", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=91\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Task/TaskController.php:91-102</a>"}, "queries": {"count": 24, "nb_statements": 22, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06162999999999999, "accumulated_duration_str": "61.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.2387168, "duration": 0.00762, "duration_str": "7.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 12.364}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4268742, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 12.364, "width_percent": 1.347}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.43503, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 13.711, "width_percent": 1.055}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.4374561, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 14.766, "width_percent": 1.006}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4505768, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 15.772, "width_percent": 1.85}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.455549, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 17.621, "width_percent": 1.233}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.473758, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 18.854, "width_percent": 3.245}, {"sql": "select count(*) as aggregate from `users` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.093703, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 22.1, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `users` where `id` = '23'", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.097575, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 24.193, "width_percent": 1.087}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.189754, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "TaskService.php:44", "source": {"index": 10, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FTask%2FTaskService.php&line=44", "ajax": false, "filename": "TaskService.php", "line": "44"}, "connection": "blueorange", "explain": null, "start_percent": 25.28, "width_percent": 0}, {"sql": "insert into `tasks` (`chatting_id`, `parent_task_id`, `title`, `description`, `deadline`, `priority`, `taskid`, `creator_id`, `updated_at`, `created_at`) values (null, null, 'Demo Task for Testing Dropzone file uploader', '<p>Demo Task for Testing Dropzone file uploader Demo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploader</p>', null, 'High', 'BOT20250717164234', 1, '2025-07-17 16:42:34', '2025-07-17 16:42:34')", "type": "query", "params": [], "bindings": [null, null, "Demo Task for Testing Dropzone file uploader", "<p>Demo Task for Testing Dropzone file uploader Demo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploader</p>", null, "High", "BOT20250717164234", 1, "2025-07-17 16:42:34", "2025-07-17 16:42:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.268655, "duration": 0.01021, "duration_str": "10.21ms", "memory": 0, "memory_str": null, "filename": "TaskService.php:45", "source": {"index": 21, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FTask%2FTaskService.php&line=45", "ajax": false, "filename": "TaskService.php", "line": "45"}, "connection": "blueorange", "explain": null, "start_percent": 25.28, "width_percent": 16.567}, {"sql": "insert into `task_user` (`created_at`, `task_id`, `updated_at`, `user_id`) values ('2025-07-17 16:42:34', 15, '2025-07-17 16:42:34', '1'), ('2025-07-17 16:42:34', 15, '2025-07-17 16:42:34', '23')", "type": "query", "params": [], "bindings": ["2025-07-17 16:42:34", 15, "2025-07-17 16:42:34", "1", "2025-07-17 16:42:34", 15, "2025-07-17 16:42:34", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 57}, {"index": 15, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.28126, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "TaskService.php:57", "source": {"index": 11, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FTask%2FTaskService.php&line=57", "ajax": false, "filename": "TaskService.php", "line": "57"}, "connection": "blueorange", "explain": null, "start_percent": 41.847, "width_percent": 3.31}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `note`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('nuxt3_lifecycle.png', 'tasks/BOT20250717164234/1ieRseOAn09oOlgqM7rhn3Gn0EWBeKmqehNynev3.png', 'image/png', 172822, 'nuxt3_lifecycle.png', null, 15, 'App\\\\Models\\\\Task\\\\Task', 1, '2025-07-17 16:42:34', '2025-07-17 16:42:34')", "type": "query", "params": [], "bindings": ["nuxt3_lifecycle.png", "tasks/BOT20250717164234/1ieRseOAn09oOlgqM7rhn3Gn0EWBeKmqehNynev3.png", "image/png", 172822, "nuxt3_lifecycle.png", null, 15, "App\\Models\\Task\\Task", 1, "2025-07-17 16:42:34", "2025-07-17 16:42:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, {"index": 18, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 62}, {"index": 22, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5514219, "duration": 0.01534, "duration_str": "15.34ms", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:30", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=30", "ajax": false, "filename": "FileMediaHelper.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 45.157, "width_percent": 24.89}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `note`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('SI-App __ User Details.pdf', 'tasks/BOT20250717164234/fn4SvPGSRRmlHaD90WBr4kPgeAEBj7qw1vCZJe9a.pdf', 'application/pdf', 1267698, 'SI-App __ User Details.pdf', null, 15, 'App\\\\Models\\\\Task\\\\Task', 1, '2025-07-17 16:42:34', '2025-07-17 16:42:34')", "type": "query", "params": [], "bindings": ["SI-App __ User Details.pdf", "tasks/BOT20250717164234/fn4SvPGSRRmlHaD90WBr4kPgeAEBj7qw1vCZJe9a.pdf", "application/pdf", 1267698, "SI-App __ User Details.pdf", null, 15, "App\\Models\\Task\\Task", 1, "2025-07-17 16:42:34", "2025-07-17 16:42:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, {"index": 18, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 62}, {"index": 22, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.581871, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:30", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=30", "ajax": false, "filename": "FileMediaHelper.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 70.047, "width_percent": 1.428}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `note`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('<PERSON><PERSON><PERSON> (<PERSON>).pdf', 'tasks/BOT20250717164234/c7sgmLPp8GonozXAWMY9kgPv3jjkTQZZwFiSeqJp.pdf', 'application/pdf', 8224, '<PERSON><PERSON><PERSON> (<PERSON>).pdf', null, 15, 'App\\\\Models\\\\Task\\\\Task', 1, '2025-07-17 16:42:34', '2025-07-17 16:42:34')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> (Neil).pdf", "tasks/BOT20250717164234/c7sgmLPp8GonozXAWMY9kgPv3jjkTQZZwFiSeqJp.pdf", "application/pdf", 8224, "<PERSON><PERSON><PERSON> (Neil).pdf", null, 15, "App\\Models\\Task\\Task", 1, "2025-07-17 16:42:34", "2025-07-17 16:42:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, {"index": 18, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 62}, {"index": 22, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.588687, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:30", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=30", "ajax": false, "filename": "FileMediaHelper.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 71.475, "width_percent": 1.509}, {"sql": "select `users`.`id` from `users` inner join `task_user` on `users`.`id` = `task_user`.`user_id` where `task_user`.`task_id` = 15 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 199}, {"index": 18, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 66}, {"index": 22, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.59255, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "TaskService.php:199", "source": {"index": 17, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FTask%2FTaskService.php&line=199", "ajax": false, "filename": "TaskService.php", "line": "199"}, "connection": "blueorange", "explain": null, "start_percent": 72.984, "width_percent": 3.975}, {"sql": "select `id`, `name`, `email` from `users` where `id` in (1, 23) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 204}, {"index": 16, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 200}, {"index": 17, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 66}, {"index": 21, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}], "start": **********.597559, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:204", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=204", "ajax": false, "filename": "TaskRepository.php", "line": "204"}, "connection": "blueorange", "explain": null, "start_percent": 76.959, "width_percent": 1.46}, {"sql": "select * from `employees` where `employees`.`user_id` in (1, 23) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 204}, {"index": 21, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 200}, {"index": 22, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 66}, {"index": 26, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}], "start": **********.6050382, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:204", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=204", "ajax": false, "filename": "TaskRepository.php", "line": "204"}, "connection": "blueorange", "explain": null, "start_percent": 78.42, "width_percent": 1.866}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": null, "name": "app/Notifications/Administration/Task/TaskCreateNotification.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Notifications\\Administration\\Task\\TaskCreateNotification.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 20}], "start": 1752748957.91467, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 80.286, "width_percent": 1.541}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('e9cb6c38-9a3a-47c3-bdfa-581901876fa4', 'App\\\\Notifications\\\\Administration\\\\Task\\\\TaskCreateNotification', '{\\\"url\\\":\\\"https:\\\\/\\\\/blueorange.test\\\\/task\\\\/show\\\\/pZJbBNxlXxeMR4OP\\\\/BOT20250717164234\\\",\\\"icon\\\":\\\"brand-stackshare\\\",\\\"title\\\":\\\"New Task Arrived\\\",\\\"message\\\":\\\"A New Task Has Been Assigned To You By Developer\\\"}', null, 1, 'App\\\\Models\\\\User', '2025-07-17 16:42:37', '2025-07-17 16:42:37')", "type": "query", "params": [], "bindings": ["e9cb6c38-9a3a-47c3-bdfa-581901876fa4", "App\\Notifications\\Administration\\Task\\TaskCreateNotification", "{\"url\":\"https:\\/\\/blueorange.test\\/task\\/show\\/pZJbBNxlXxeMR4OP\\/BOT20250717164234\",\"icon\":\"brand-stackshare\",\"title\":\"New Task Arrived\",\"message\":\"A New Task Has Been Assigned To You By Developer\"}", null, 1, "App\\Models\\User", "2025-07-17 16:42:37", "2025-07-17 16:42:37"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 101}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 79}], "start": 1752748957.9189558, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:19", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=19", "ajax": false, "filename": "DatabaseChannel.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 81.827, "width_percent": 1.85}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"f3c4d370-2d76-4262-826d-f1ad4698b1de\\\",\\\"displayName\\\":\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\Task\\\\\\\\NewTaskMail\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\",\\\"command\\\":\\\"O:34:\\\\\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\\\\\":15:{s:8:\\\\\\\"mailable\\\\\\\";O:40:\\\\\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\Task\\\\\\\\NewTaskMail\\\\\\\":4:{s:4:\\\\\\\"data\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:20:\\\\\\\"App\\\\\\\\Models\\\\\\\\Task\\\\\\\\Task\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:15;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:4:\\\\\\\"user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:1:{i:0;s:8:\\\\\\\"employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";N;s:7:\\\\\\\"address\\\\\\\";s:20:\\\\\\\"<EMAIL>\\\\\\\";}}s:6:\\\\\\\"mailer\\\\\\\";s:4:\\\\\\\"smtp\\\\\\\";}s:5:\\\\\\\"tries\\\\\\\";N;s:7:\\\\\\\"timeout\\\\\\\";N;s:13:\\\\\\\"maxExceptions\\\\\\\";N;s:17:\\\\\\\"shouldBeEncrypted\\\\\\\";b:0;s:10:\\\\\\\"connection\\\\\\\";N;s:5:\\\\\\\"queue\\\\\\\";N;s:15:\\\\\\\"chainConnection\\\\\\\";N;s:10:\\\\\\\"chainQueue\\\\\\\";N;s:19:\\\\\\\"chainCatchCallbacks\\\\\\\";N;s:5:\\\\\\\"delay\\\\\\\";N;s:11:\\\\\\\"afterCommit\\\\\\\";N;s:10:\\\\\\\"middleware\\\\\\\";a:0:{}s:7:\\\\\\\"chained\\\\\\\";a:0:{}s:3:\\\\\\\"job\\\\\\\";N;}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"f3c4d370-2d76-4262-826d-f1ad4698b1de\",\"displayName\":\"App\\\\Mail\\\\Administration\\\\Task\\\\NewTaskMail\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Mail\\\\SendQueuedMailable\",\"command\":\"O:34:\\\"Illuminate\\\\Mail\\\\SendQueuedMailable\\\":15:{s:8:\\\"mailable\\\";O:40:\\\"App\\\\Mail\\\\Administration\\\\Task\\\\NewTaskMail\\\":4:{s:4:\\\"data\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:20:\\\"App\\\\Models\\\\Task\\\\Task\\\";s:2:\\\"id\\\";i:15;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:4:\\\"user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"to\\\";a:1:{i:0;a:2:{s:4:\\\"name\\\";N;s:7:\\\"address\\\";s:20:\\\"<EMAIL>\\\";}}s:6:\\\"mailer\\\";s:4:\\\"smtp\\\";}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:13:\\\"maxExceptions\\\";N;s:17:\\\"shouldBeEncrypted\\\";b:0;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:3:\\\"job\\\";N;}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 59}], "start": **********.233512, "duration": 0.007019999999999999, "duration_str": "7.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 83.677, "width_percent": 11.391}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('eb6c1142-13d6-484c-8faa-3c6718de7aa1', 'App\\\\Notifications\\\\Administration\\\\Task\\\\TaskCreateNotification', '{\\\"url\\\":\\\"https:\\\\/\\\\/blueorange.test\\\\/task\\\\/show\\\\/pZJbBNxlXxeMR4OP\\\\/BOT20250717164234\\\",\\\"icon\\\":\\\"brand-stackshare\\\",\\\"title\\\":\\\"New Task Arrived\\\",\\\"message\\\":\\\"A New Task Has Been Assigned To You By Developer\\\"}', null, 23, 'App\\\\Models\\\\User', '2025-07-17 16:42:39', '2025-07-17 16:42:39')", "type": "query", "params": [], "bindings": ["eb6c1142-13d6-484c-8faa-3c6718de7aa1", "App\\Notifications\\Administration\\Task\\TaskCreateNotification", "{\"url\":\"https:\\/\\/blueorange.test\\/task\\/show\\/pZJbBNxlXxeMR4OP\\/BOT20250717164234\",\"icon\":\"brand-stackshare\",\"title\":\"New Task Arrived\",\"message\":\"A New Task Has Been Assigned To You By Developer\"}", null, 23, "App\\Models\\User", "2025-07-17 16:42:39", "2025-07-17 16:42:39"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 101}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 79}], "start": **********.268574, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:19", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=19", "ajax": false, "filename": "DatabaseChannel.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 95.067, "width_percent": 3.44}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"2f62f314-2cea-416d-897f-5839462aa8a9\\\",\\\"displayName\\\":\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\Task\\\\\\\\NewTaskMail\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\",\\\"command\\\":\\\"O:34:\\\\\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\\\\\":15:{s:8:\\\\\\\"mailable\\\\\\\";O:40:\\\\\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\Task\\\\\\\\NewTaskMail\\\\\\\":4:{s:4:\\\\\\\"data\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:20:\\\\\\\"App\\\\\\\\Models\\\\\\\\Task\\\\\\\\Task\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:15;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:4:\\\\\\\"user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:23;s:9:\\\\\\\"relations\\\\\\\";a:1:{i:0;s:8:\\\\\\\"employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";N;s:7:\\\\\\\"address\\\\\\\";s:24:\\\\\\\"<EMAIL>\\\\\\\";}}s:6:\\\\\\\"mailer\\\\\\\";s:4:\\\\\\\"smtp\\\\\\\";}s:5:\\\\\\\"tries\\\\\\\";N;s:7:\\\\\\\"timeout\\\\\\\";N;s:13:\\\\\\\"maxExceptions\\\\\\\";N;s:17:\\\\\\\"shouldBeEncrypted\\\\\\\";b:0;s:10:\\\\\\\"connection\\\\\\\";N;s:5:\\\\\\\"queue\\\\\\\";N;s:15:\\\\\\\"chainConnection\\\\\\\";N;s:10:\\\\\\\"chainQueue\\\\\\\";N;s:19:\\\\\\\"chainCatchCallbacks\\\\\\\";N;s:5:\\\\\\\"delay\\\\\\\";N;s:11:\\\\\\\"afterCommit\\\\\\\";N;s:10:\\\\\\\"middleware\\\\\\\";a:0:{}s:7:\\\\\\\"chained\\\\\\\";a:0:{}s:3:\\\\\\\"job\\\\\\\";N;}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"2f62f314-2cea-416d-897f-5839462aa8a9\",\"displayName\":\"App\\\\Mail\\\\Administration\\\\Task\\\\NewTaskMail\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Mail\\\\SendQueuedMailable\",\"command\":\"O:34:\\\"Illuminate\\\\Mail\\\\SendQueuedMailable\\\":15:{s:8:\\\"mailable\\\";O:40:\\\"App\\\\Mail\\\\Administration\\\\Task\\\\NewTaskMail\\\":4:{s:4:\\\"data\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:20:\\\"App\\\\Models\\\\Task\\\\Task\\\";s:2:\\\"id\\\";i:15;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:4:\\\"user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:23;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"to\\\";a:1:{i:0;a:2:{s:4:\\\"name\\\";N;s:7:\\\"address\\\";s:24:\\\"<EMAIL>\\\";}}s:6:\\\"mailer\\\";s:4:\\\"smtp\\\";}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:13:\\\"maxExceptions\\\";N;s:17:\\\"shouldBeEncrypted\\\";b:0;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:3:\\\"job\\\";N;}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 59}], "start": **********.273922, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 98.507, "width_percent": 1.493}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.334089, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "TaskService.php:44", "source": {"index": 9, "namespace": null, "name": "app/Services/Administration/Task/TaskService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Task\\TaskService.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FTask%2FTaskService.php&line=44", "ajax": false, "filename": "TaskService.php", "line": "44"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1103128200 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103128200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483509, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-132838691 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132838691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489548, "xdebug_link": null}]}, "session": {"_token": "BoRJF60RpQV2za0lpI7XuPnduk4veKzFXR8OeXlM", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/task/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751882401\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Task assigned successfully.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/task/store", "action_name": "administration.task.store", "controller_action": "App\\Http\\Controllers\\Administration\\Task\\TaskController@store", "uri": "POST task/store", "controller": "App\\Http\\Controllers\\Administration\\Task\\TaskController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=91\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/task", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=91\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Task/TaskController.php:91-102</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Task Create", "duration": "7.59s", "peak_memory": "38MB", "response": "Redirect to https://blueorange.test/task/show/pZJbBNxlXxeMR4OP/BOT20250717164234", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-163029148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-163029148\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1503218458 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BoRJF60RpQV2za0lpI7XuPnduk4veKzFXR8OeXlM</span>\"\n  \"<span class=sf-dump-key>parent_task_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>deadline</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Demo Task for Testing Dropzone file uploader</span>\"\n  \"<span class=sf-dump-key>users</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"184 characters\">&lt;p&gt;Demo Task for Testing Dropzone file uploader Demo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploaderDemo Task for Testing Dropzone file uploader&lt;/p&gt;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503218458\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1125863365 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">1450294</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryXBFAUucaUNTcnDph</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">https://blueorange.test/task/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6ImUzTm1ObDBHTmxPTmZEM2JyQVh4MHc9PSIsInZhbHVlIjoiTEZCbTh4aHhQL2xHaUtLSUR3M1Z6L1JjU2JXRnFVaU5GR0JpVzBKc3pPOG5aTFRtYWkzS0VYL012b1pwMG5OeDVXcVllNU15bXR5a2dmUDFjN1ZGSVI1dXI3M3lHaVN4bW9YRWtxNzJwNVhSYnJzb1ZGYzF5anhza1RwMDY0K1AiLCJtYWMiOiJiOWYzNTUxMjIwZjNmYjE5ODg4MzMwOTBjZDJjMDE2MzllNDAwYWIyZmQzZWI0NWJhNzA3YTAwOTdmMmVjMmM3IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6InFXS1grS2FwNUY1a1d4cDJJR3hWOVE9PSIsInZhbHVlIjoiQWdzekp0TFV6RXc4VXIvZ25vWHp5dDVMVHlNQW41QTk4Q1FicUxMTGl0MER3QlB2MmJMb2kvNkN4bmxNelRHK0NrcmpDSWVnTEw3d1pMUGJVNXRnNTVuYWM4VjZ3NlJuSUh5SkZvc2dKZ3RjOENlVVNYM0toU1IyVURLSWltemoiLCJtYWMiOiIyOTBjZDY2YzAwMjAxODZhNzA1NzY3NzlhMTAzYWNhMzYwMjQ1MGQ0MjU4Nzk1ZDQ0ZGY3OTkxYjg1NzNjNzQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125863365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2037543978 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BoRJF60RpQV2za0lpI7XuPnduk4veKzFXR8OeXlM</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DZ9YfS8xivYk65PW1xnkImo3rhZyDDjJ8s2n4eNk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037543978\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-733079511 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 10:42:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">https://blueorange.test/task/show/pZJbBNxlXxeMR4OP/BOT20250717164234</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733079511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1350331431 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BoRJF60RpQV2za0lpI7XuPnduk4veKzFXR8OeXlM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://blueorange.test/task/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751882401</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"439 characters\">{&quot;title&quot;:&quot;Task assigned successfully.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350331431\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/task/store", "action_name": "administration.task.store", "controller_action": "App\\Http\\Controllers\\Administration\\Task\\TaskController@store"}, "badge": "302 Found"}}